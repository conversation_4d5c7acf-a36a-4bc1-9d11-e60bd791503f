-- section SC<PERSON>EMA
DROP SCHEMA IF EXISTS app_catalog CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_catalog
;

GRANT USAGE ON SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_catalog TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_catalog
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor FIELD_TYPE
CREATE TYPE app_catalog.FIELD_TYPE AS ENUM(
  'custom',
  'select',
  'multiselect'
)
;

-- !section
--------------------------------------------------------------------------------
-- section TABLES
--------------------------------------------------------------------------------
-- anchor category
CREATE TABLE app_catalog.category (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  parent_category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  NAME JSONB NOT NULL,
  description JSONB,
  icon TEXT,
  cover TEXT,
  slug TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (NAME)
)
;

-- anchor activity
CREATE TABLE app_catalog.activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  NAME JSONB NOT NULL,
  description JSONB,
  color JSONB CHECK (
    color IS NULL
    OR app_core.is_valid_color_jsonb (color)
  ),
  slug TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (category_id, NAME)
)
;

-- anchor tag
CREATE TABLE app_catalog.tag (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  NAME JSONB NOT NULL,
  description JSONB,
  slug TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_tag
CREATE TABLE app_catalog.activity_tag (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE SET NULL,
  tag_id UUID REFERENCES app_catalog.tag (id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (tag_id, activity_id)
)
;

-- anchor field
CREATE TABLE app_catalog.field (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  NAME JSONB NOT NULL,
  description JSONB,
  TYPE app_catalog.FIELD_TYPE NOT NULL DEFAULT 'custom',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor category_field
CREATE TABLE app_catalog.category_field (
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE SET NULL,
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE SET NULL,
  PRIMARY KEY (field_id, category_id)
)
;

-- anchor activity_field
CREATE TABLE app_catalog.activity_field (
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE SET NULL,
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE SET NULL,
  PRIMARY KEY (field_id, activity_id)
)
;

-- anchor field_option
CREATE TABLE app_catalog.field_option (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  field_id UUID REFERENCES app_catalog.field (id) ON DELETE CASCADE,
  NAME JSONB NOT NULL,
  description JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor category_field_option
CREATE TABLE app_catalog.category_field_option (
  field_option_id UUID REFERENCES app_catalog.field_option (id) ON DELETE SET NULL,
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  PRIMARY KEY (field_option_id, category_id)
)
;

-- anchor activity_field_option
CREATE TABLE app_catalog.activity_field_option (
  field_option_id UUID REFERENCES app_catalog.field_option (id) ON DELETE CASCADE,
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  PRIMARY KEY (field_option_id, activity_id)
)
;

-- anchor pricing
CREATE TABLE app_catalog.pricing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  NAME JSONB NOT NULL,
  description JSONB,
  icon TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_pricing
CREATE TABLE app_catalog.activity_pricing (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  pricing_id UUID REFERENCES app_catalog.pricing (id) ON DELETE CASCADE,
  PRIMARY KEY (activity_id, pricing_id)
)
;

-- anchor category_pricing
CREATE TABLE app_catalog.category_pricing (
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  pricing_id UUID REFERENCES app_catalog.pricing (id) ON DELETE CASCADE,
  PRIMARY KEY (category_id, pricing_id)
)
;

-- anchor service
CREATE TABLE app_catalog.service (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
  NAME JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor activity_service
CREATE TABLE app_catalog.activity_service (
  activity_id UUID REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  service_id UUID REFERENCES app_catalog.service (id) ON DELETE CASCADE,
  PRIMARY KEY (activity_id, service_id)
)
;

-- anchor category_service
CREATE TABLE app_catalog.category_service (
  category_id UUID REFERENCES app_catalog.category (id) ON DELETE CASCADE,
  service_id UUID REFERENCES app_catalog.service (id) ON DELETE CASCADE,
  PRIMARY KEY (category_id, service_id)
)
;

-- !section
-- section TRIGGERS
-- anchor category
CREATE TRIGGER category_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.category FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

CREATE TRIGGER category_check_circular_dependency BEFORE INSERT
OR
UPDATE ON app_catalog.category FOR EACH ROW
EXECUTE FUNCTION app_core.check_circular_dependency (
  'app_catalog',
  'category',
  'id',
  'parent_category_id',
  'Circular dependency detected in category hierarchy'
)
;

-- anchor activity
CREATE TRIGGER activity_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.activity FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor tag
CREATE TRIGGER tag_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.tag FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor field
CREATE TRIGGER field_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.field FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor field_option
CREATE TRIGGER field_option_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.field_option FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor pricing
CREATE TRIGGER pricing_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.pricing FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

-- anchor service
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_catalog.service FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name')
;

-- !section
-- section RLS POLICIES
-- anchor category
ALTER TABLE app_catalog.category ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_select_all" ON app_catalog.category FOR
SELECT
  USING (TRUE)
;

-- anchor activity
ALTER TABLE app_catalog.activity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_select_all" ON app_catalog.activity FOR
SELECT
  USING (TRUE)
;

-- anchor tag
ALTER TABLE app_catalog.tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "tag_select_all" ON app_catalog.tag FOR
SELECT
  USING (TRUE)
;

-- anchor activity_tag
ALTER TABLE app_catalog.activity_tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_tag_select_all" ON app_catalog.activity_tag FOR
SELECT
  USING (TRUE)
;

-- anchor field
ALTER TABLE app_catalog.field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "field_select_all" ON app_catalog.field FOR
SELECT
  USING (TRUE)
;

-- anchor category_field
ALTER TABLE app_catalog.category_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_field_select_all" ON app_catalog.category_field FOR
SELECT
  USING (TRUE)
;

-- anchor activity_field
ALTER TABLE app_catalog.activity_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_field_select_all" ON app_catalog.activity_field FOR
SELECT
  USING (TRUE)
;

-- anchor field_option
ALTER TABLE app_catalog.field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "field_option_select_all" ON app_catalog.field_option FOR
SELECT
  USING (TRUE)
;

-- anchor category_field_option
ALTER TABLE app_catalog.category_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_field_option_select_all" ON app_catalog.category_field_option FOR
SELECT
  USING (TRUE)
;

-- anchor activity_field_option
ALTER TABLE app_catalog.activity_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_field_option_select_all" ON app_catalog.activity_field_option FOR
SELECT
  USING (TRUE)
;

-- anchor pricing
ALTER TABLE app_catalog.pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "pricing_select_all" ON app_catalog.pricing FOR
SELECT
  USING (TRUE)
;

-- anchor service
ALTER TABLE app_catalog.service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_select_all" ON app_catalog.service FOR
SELECT
  USING (TRUE)
;

-- anchor activity_service
ALTER TABLE app_catalog.activity_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_service_select_all" ON app_catalog.activity_service FOR
SELECT
  USING (TRUE)
;

-- anchor category_service
ALTER TABLE app_catalog.category_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_service_select_all" ON app_catalog.category_service FOR
SELECT
  USING (TRUE)
;

-- anchor activity_pricing
ALTER TABLE app_catalog.activity_pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_pricing_select_all" ON app_catalog.activity_pricing FOR
SELECT
  USING (TRUE)
;

-- anchor category_pricing
ALTER TABLE app_catalog.category_pricing ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_pricing_select_all" ON app_catalog.category_pricing FOR
SELECT
  USING (TRUE)
;

-- !section