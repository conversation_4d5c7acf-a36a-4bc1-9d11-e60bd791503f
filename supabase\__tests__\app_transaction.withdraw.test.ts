import { describe, test, expect, beforeAll, afterAll } from "vitest";
import { userClient, serviceClient, user } from "./setup";

describe("Withdrawal Request Tests", () => {
  let originalAmount: number;

  beforeAll(async () => {
    const { data: config } = await serviceClient
      .schema("app_transaction")
      .from("config")
      .select("minimum_soda_withdrawal_amount")
      .single();

    originalAmount = config?.minimum_soda_withdrawal_amount ?? 0;

    await serviceClient
      .schema("app_transaction")
      .from("config")
      .update({
        minimum_soda_withdrawal_amount: 100
      })
      .eq("id", true);

    await serviceClient.schema("app_transaction").from("wallet").insert({
      user_id: user.id,
      soda_balance: 1000
    });
  });

  afterAll(async () => {
    await serviceClient
      .schema("app_transaction")
      .from("config")
      .update({
        minimum_soda_withdrawal_amount: originalAmount
      })
      .eq("id", true);
  });

  test("Allow withdrawal when amount meets minimum", async () => {
    const inserting = await userClient
      .schema("app_transaction")
      .from("withdrawal_request")
      .insert({
        soda_amount: 100,
        currency: "TRY"
      });

    expect(inserting.error).toBe(null);
  });

  test("Disallow withdrawal when amount is below minimum", async () => {
    const inserting = await userClient
      .schema("app_transaction")
      .from("withdrawal_request")
      .insert({
        soda_amount: 99,
        currency: "TRY"
      });

    expect(inserting.error).not.toBe(null);
  });

  test("Disallow withdrawal when wallet balance is insufficient", async () => {
    const inserting = await userClient
      .schema("app_transaction")
      .from("withdrawal_request")
      .insert({
        soda_amount: 1001, // More than the initial 1000 balance
        currency: "TRY"
      });

    expect(inserting.error).not.toBe(null);
  });
});
