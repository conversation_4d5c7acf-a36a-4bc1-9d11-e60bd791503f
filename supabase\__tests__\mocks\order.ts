import { afterEach, beforeEach, expect } from "vitest";
import { serviceClient } from "../setup";
import { MockUser } from "./user";
import { MockService } from "./service";

export type MockOrder = {
  id: string;
};

export function mockOrderEach(
  customer: <PERSON>ck<PERSON><PERSON>,
  provider: MockUser,
  service: MockService
) {
  const order: MockOrder = { id: "" };

  beforeEach(async () => {
    if (!customer.data) throw new Error("User data is null");
    if (!provider.data) throw new Error("Provider data is null");
    if (!customer.client) throw new Error("User client is null");

    // Reset balances
    await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .upsert([
        { user_id: customer.data.id, soda_balance: 1000 },
        { user_id: provider.data.id, soda_balance: 0 }
      ]);

    // User submits an order
    const orderInsert = await customer.client
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: service.providerServiceId,
        p_unit_count: 1
      });

    expect(orderInsert.data?.order_status).toBe("pending");

    order.id = String(orderInsert.data?.id);
  });

  afterEach(async () => {
    if (!order.id) throw new Error("Order ID not set for cleaning");

    // Clean up orders
    await serviceClient
      .schema("app_provider")
      .from("order")
      .delete()
      .eq("id", order.id);

    // Clean up order archive
    await serviceClient
      .schema("app_provider")
      .from("order_archive")
      .delete()
      .eq("id", order.id);

    // Clean up order logs
    await serviceClient
      .schema("app_provider")
      .from("order_log")
      .delete()
      .eq("order_id", order.id);
  });

  return order;
}

export function mockCompletedOrderEach(order: MockOrder, provider: MockUser) {
  beforeEach(async () => {
    if (!order.id) throw new Error("Order ID not set for mocking");

    // Provider accepts the order
    const acceptUpdate = await provider.client
      ?.schema("app_provider")
      .from("order")
      .update({ order_status: "accepted" })
      .eq("id", order.id)
      .select()
      .single();

    expect(acceptUpdate?.data?.order_status).toBe("accepted");

    // Provider completes the order
    const completeUpdate = await provider.client
      ?.schema("app_provider")
      .from("order")
      .update({ order_status: "completed" })
      .eq("id", order.id)
      .select()
      .single();

    expect(completeUpdate?.data?.order_status).toBe("completed");
  });

  afterEach(async () => {
    if (!provider.data) throw new Error("Provider data is null");
    await serviceClient
      .schema("app_provider")
      .from("performance")
      .delete()
      .eq("user_id", provider.data.id);
  });
}
