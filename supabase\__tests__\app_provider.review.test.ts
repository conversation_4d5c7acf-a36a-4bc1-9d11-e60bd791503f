import { test, expect, beforeEach, afterEach, describe } from "vitest";
import {
  userClient,
  providerClient,
  user,
  serviceClient,
  provider,
  adminClient
} from "./setup";
import { providerServiceId, selectedActivityId } from "./setup-provider-data";
import { Tables } from "shared/lib/supabase/database";

let orderId: string;

beforeEach(async () => {
  // give user sufficient balance
  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .upsert({ user_id: user.id, soda_balance: 1000 });

  // Create an order with a completed status.
  const orderInsert = await userClient
    .schema("app_provider")
    .rpc("submit_order", {
      p_service_id: providerServiceId,
      p_unit_count: 1
    });

  expect(orderInsert.data?.order_status).toBe("pending");

  orderId = String(orderInsert.data?.id);

  const acceptUpdate = await providerClient
    .schema("app_provider")
    .from("order")
    .update({ order_status: "accepted" })
    .eq("id", orderId);

  expect(acceptUpdate.error).toBeNull();

  const completeUpdate = await providerClient
    .schema("app_provider")
    .from("order")
    .update({ order_status: "completed" })
    .eq("id", orderId)
    .select()
    .single();

  expect(completeUpdate.data?.order_status).toBe("completed");
});

afterEach(async () => {
  await serviceClient
    .schema("app_provider")
    .from("review")
    .delete()
    .eq("user_id", user.id);

  await serviceClient
    .schema("app_provider")
    .from("review_pass")
    .delete()
    .eq("user_id", user.id);

  await serviceClient
    .schema("app_provider")
    .from("order")
    .delete()
    .eq("id", orderId);

  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .update({ soda_balance: 0, cap_balance: 0 })
    .eq("user_id", user.id);
});

describe("Review Pass Creation", () => {
  test("Completing an order creates review pass", async () => {
    const { data: reviewPass, error: reviewPassError } = await userClient
      .schema("app_provider")
      .from("review_pass")
      .select("*")
      .eq("user_id", user.id)
      .eq("order_id", orderId)
      .single();

    expect(reviewPassError).toBeNull();
    expect(reviewPass).toBeDefined();
    expect(reviewPass?.user_id).toBe(user.id);
    expect(reviewPass?.provider_id).toBe(provider.id);
    expect(reviewPass?.activity_id).toBe(selectedActivityId);
    expect(reviewPass?.order_id).toBe(orderId);
  });
});

describe("Review Creation and Rewards", () => {
  let reviewPass: Tables<{ schema: "app_provider" }, "review_pass"> | null;

  beforeEach(async () => {
    const { data: fetchedReviewPass } = await userClient
      .schema("app_provider")
      .from("review_pass")
      .select("*")
      .eq("user_id", user.id)
      .eq("order_id", orderId)
      .single();

    reviewPass = fetchedReviewPass;
    expect(reviewPass).toBeDefined();
  });

  test("Inserting a review rewards caps", async () => {
    // Get initial cap balance
    const { data: initialWallet, error: initialWalletError } =
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("cap_balance")
        .eq("user_id", user.id)
        .single();

    expect(initialWalletError).toBeNull();
    expect(initialWallet).toBeDefined();

    const initialCapBalance = initialWallet?.cap_balance || 0;

    // Insert review
    if (!reviewPass) throw new Error("reviewPass not defined");
    const { data: reviewInsert, error: reviewError } = await userClient
      .schema("app_provider")
      .from("review")
      .insert({
        id: reviewPass.id,
        user_id: user.id,
        rating: 5,
        comment: "Great service!",
        comment_locale: "en"
      })
      .select("*")
      .single();

    expect(reviewError).toBeNull();
    expect(reviewInsert).toBeDefined();

    // Get cap reward amount from config
    const { data: config, error: configError } = await serviceClient
      .schema("app_provider")
      .from("config")
      .select("cap_reward_for_review")
      .single();

    expect(configError).toBeNull();
    expect(config).toBeDefined();
    const capReward = config?.cap_reward_for_review || 0;

    // Get final cap balance
    const { data: finalWallet, error: finalWalletError } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", user.id)
      .single();

    expect(finalWalletError).toBeNull();
    expect(finalWallet).toBeDefined();
    const finalCapBalance = finalWallet?.cap_balance || 0;

    // Assert that cap balance increased by the reward amount
    expect(finalCapBalance).toBe(initialCapBalance + capReward);
  });

  describe("When a review is created", () => {
    let review: Tables<{ schema: "app_provider" }, "review"> | null;

    beforeEach(async () => {
      if (!reviewPass) throw new Error("reviewPass not defined");
      const { data: createdReview, error: reviewError } = await userClient
        .schema("app_provider")
        .from("review")
        .insert({
          id: reviewPass.id,
          user_id: user.id,
          rating: 5,
          comment: "Great service!",
          comment_locale: "en"
        })
        .select("*")
        .single();
      review = createdReview;
      expect(reviewError).toBeNull();
      expect(review).toBeDefined();
    });

    test("Review pass can be used to create review", async () => {
      expect(review?.id).toBe(reviewPass?.id);
      expect(review?.user_id).toBe(user.id);
      expect(review?.rating).toBe(5);
      expect(review?.comment).toBe("Great service!");
    });

    test("Cannot update review", async () => {
      const originalComment = review!.comment;
      await userClient
        .schema("app_provider")
        .from("review")
        .update({ comment: "Updated comment" })
        .eq("id", review!.id);
      // No error expected as we are just verifying with a select statement

      // Verify the comment has not been updated
      const { data: updatedReview, error: fetchError } = await userClient
        .schema("app_provider")
        .from("review")
        .select("comment")
        .eq("id", review!.id)
        .single();

      expect(fetchError).toBeNull();
      expect(updatedReview?.comment).toBe(originalComment);
    });

    test("Cannot select unapproved review", async () => {
      const { data: unapprovedReview, error: unapprovedReviewError } =
        await providerClient
          .schema("app_provider")
          .from("review")
          .select("*")
          .eq("id", review!.id)
          .single();

      expect(unapprovedReviewError).not.toBeNull();
      expect(unapprovedReview).toBeNull();
    });

    test("Approving a review allows it to be selected", async () => {
      if (!review) return;

      // Add review to approved_review table
      const { error: approveError } = await adminClient
        .schema("app_provider")
        .from("approved_review")
        .insert({
          review_id: review.id,
          user_id: user.id
        });

      expect(approveError).toBeNull();

      // Try to select it again as providerClient (it should succeed now that it's approved)
      const { data: approvedReview, error: approvedReviewError } =
        await providerClient
          .schema("app_provider")
          .from("review")
          .select("*")
          .eq("id", review.id)
          .single();

      expect(approvedReviewError).toBeNull();
      expect(approvedReview).toBeDefined();
      expect(approvedReview?.id).toBe(review.id);
    });
  });
});
