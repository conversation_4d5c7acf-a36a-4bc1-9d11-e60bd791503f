import { SupabaseClient, User } from "@supabase/supabase-js";
import { Database } from "shared/lib/supabase/database";
import { createClient as createServiceClient } from "shared/lib/supabase/service";
import { createClient } from "@supabase/supabase-js";
import { afterAll, beforeAll } from "vitest";
import { randomUUID } from "node:crypto";

const serviceClient = createServiceClient();

export type MockUser = {
  client: SupabaseClient<Database> | null;
  data: User | null;
};

export async function createAndSignInUser(role: string): Promise<MockUser> {
  const userId = randomUUID();

  const userCreation = await serviceClient.auth.admin.createUser({
    id: userId,
    email: `${role}-${userId}@example.com`,
    password: "password123",
    email_confirm: true
  });

  if (!userCreation.data.user) {
    throw `Failed to create ${role} user`;
  }

  // assign role to user
  await serviceClient.schema("app_access").rpc("assign_role_to_user", {
    v_user_id: userCreation.data.user.id,
    v_role_name: role
  });

  const client = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  await client.auth.signInWithPassword({
    email: userCreation.data.user.email ?? "",
    password: "password123"
  });

  return { client, data: userCreation.data.user };
}

export function mockUser(role: string) {
  const user: MockUser = {
    client: null,
    data: null
  };

  beforeAll(async () => {
    const { client, data } = await createAndSignInUser(role);

    user.client = client;
    user.data = data;
  });

  afterAll(async () => {
    if (user.data) {
      await serviceClient.auth.admin.deleteUser(user.data.id);
    }
  });

  return user;
}

export function mockProvider() {
  return mockUser("provider");
}

export function mockCustomer() {
  return mockUser("customer");
}

export function mockAdmin() {
  return mockUser("admin");
}
