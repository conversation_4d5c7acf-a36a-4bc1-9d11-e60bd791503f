import { test, expect, describe, afterEach } from "vitest";
import { serviceClient } from "./setup";
import { mockCustomer } from "./mocks/user";

const customer = mockCustomer();

afterEach(async () => {
  if (!customer.data) throw new Error("Customer not initialized");
  await serviceClient
    .schema("app_provider")
    .from("availability")
    .delete()
    .eq("user_id", customer.data.id);
});

describe("Individual Day Availability", () => {
  test("Can insert non-overlapping individual_day availability", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "09:00:00+00",
        end_time: "10:00:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).toBeNull();
  });

  test("Cannot insert overlapping individual_day availability (full overlap)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });

    // Attempt to insert an overlapping availability
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "09:30:00+00",
        end_time: "10:30:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Can insert individual_day availability with touching boundaries", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "individual_day",
      day_of_week: "tuesday"
    });

    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "10:00:00+00",
        end_time: "11:00:00+00",
        availability_type: "individual_day",
        day_of_week: "tuesday"
      });
    expect(response.error).toBeNull();
  });

  test("Cannot insert overlapping weekdays availability (overlaps with existing individual_day)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial individual_day availability on a weekday
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "10:00:00+00",
      end_time: "11:00:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });

    // Attempt to insert a weekdays availability that overlaps
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "08:00:00+00",
        end_time: "12:00:00+00",
        availability_type: "weekdays"
      });
    expect(response.error).not.toBeNull();
  });
});

describe("Weekdays Availability", () => {
  test("Cannot insert overlapping individual_day availability (overlaps with existing weekdays)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial weekdays availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "09:00:00+00",
      end_time: "10:00:00+00",
      availability_type: "weekdays"
    });

    // Attempt to insert an overlapping individual_day availability
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "09:30:00+00",
        end_time: "10:30:00+00",
        availability_type: "individual_day",
        day_of_week: "monday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Cannot insert overlapping weekdays availability (full overlap)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial weekdays availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "09:00:00+00",
      end_time: "17:00:00+00",
      availability_type: "weekdays"
    });

    // Attempt to insert an overlapping weekdays availability
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "10:00:00+00",
        end_time: "12:00:00+00",
        availability_type: "weekdays"
      });
    expect(response.error).not.toBeNull();
  });
});

describe("Weekends Availability", () => {
  test("Cannot insert overlapping individual_day availability (overlaps with existing weekends)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial weekends availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "10:00:00+00",
      end_time: "18:00:00+00",
      availability_type: "weekends"
    });

    // Attempt to insert an overlapping individual_day availability
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "12:00:00+00",
        end_time: "13:00:00+00",
        availability_type: "individual_day",
        day_of_week: "saturday"
      });
    expect(response.error).not.toBeNull();
  });

  test("Cannot insert overlapping weekends availability (full overlap)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not initialized");
    // Insert an initial weekends availability
    await customer.client.schema("app_provider").from("availability").insert({
      user_id: customer.data.id,
      start_time: "10:00:00+00",
      end_time: "18:00:00+00",
      availability_type: "weekends"
    });

    // Attempt to insert an overlapping weekends availability
    const response = await customer.client
      .schema("app_provider")
      .from("availability")
      .insert({
        user_id: customer.data.id,
        start_time: "12:00:00+00",
        end_time: "14:00:00+00",
        availability_type: "weekends"
      });
    expect(response.error).not.toBeNull();
  });
});

test("Cannot insert availability with duration less than 15 minutes", async () => {
  if (!customer.client || !customer.data)
    throw new Error("Customer not initialized");
  const response = await customer.client
    .schema("app_provider")
    .from("availability")
    .insert({
      user_id: customer.data.id,
      start_time: "20:00:00+00",
      end_time: "20:10:00+00",
      availability_type: "individual_day",
      day_of_week: "monday"
    });
  expect(response.error).not.toBeNull();
  expect(response.error?.message).toContain("min_duration_15_minutes");
});
