import { afterAll, beforeAll, expect } from "vitest";
import { serviceClient } from "../setup";
import { randomUUID } from "node:crypto";
import { MockUser } from "./user";

export type MockService = {
  catalogActivityId: string;
  catalogServiceId: string;
  catalogPricingId: string;
  selectedActivityId: string;
  providerServiceModifierId: string;
  providerServiceId: string;
};

export function mockService(provider: MockUser) {
  const service: MockService = {
    catalogActivityId: "",
    catalogServiceId: "",
    catalogPricingId: "",
    selectedActivityId: "",
    providerServiceModifierId: "",
    providerServiceId: ""
  };

  beforeAll(async () => {
    if (!provider.data) throw new Error("Provider data is null");

    service.catalogActivityId = randomUUID();
    service.catalogServiceId = randomUUID();
    service.catalogPricingId = randomUUID();
    service.selectedActivityId = randomUUID();
    service.providerServiceModifierId = randomUUID();
    service.providerServiceId = randomUUID();

    const approveProvider = await serviceClient
      .schema("app_provider")
      .from("approved_user")
      .insert({ user_id: provider.data.id })
      .select()
      .single();
    expect(approveProvider.data?.user_id).toBe(provider.data.id);

    const setOpenForOrder = await serviceClient
      .schema("app_provider")
      .from("status")
      .insert({
        user_id: provider.data.id,
        is_open_for_orders: true
      })
      .select()
      .single();
    expect(setOpenForOrder.data?.user_id).toBe(provider.data.id);

    const catalogActivityInsert = await serviceClient
      .schema("app_catalog")
      .from("activity")
      .insert({
        id: service.catalogActivityId,
        name: { en: "Test Activity" }
      })
      .select()
      .single();
    expect(catalogActivityInsert.data?.id).toBe(service.catalogActivityId);

    const catalogServiceInsert = await serviceClient
      .schema("app_catalog")
      .from("service")
      .insert({
        id: service.catalogServiceId,
        name: { en: "Test Catalog Service" }
      })
      .select()
      .single();
    expect(catalogServiceInsert.data?.id).toBe(service.catalogServiceId);

    const catalogPricingInsert = await serviceClient
      .schema("app_catalog")
      .from("pricing")
      .insert({
        id: service.catalogPricingId,
        name: { en: "Test Pricing" }
      })
      .select()
      .single();
    expect(catalogPricingInsert.data?.id).toBe(service.catalogPricingId);

    const linkCatalogServiceToActivity = await serviceClient
      .schema("app_catalog")
      .from("activity_service")
      .insert({
        activity_id: service.catalogActivityId,
        service_id: service.catalogServiceId
      })
      .select()
      .single();
    expect(linkCatalogServiceToActivity.data?.activity_id).toBe(
      service.catalogActivityId
    );
    expect(linkCatalogServiceToActivity.data?.service_id).toBe(
      service.catalogServiceId
    );

    const linkCatalogPricingToActivity = await serviceClient
      .schema("app_catalog")
      .from("activity_pricing")
      .insert({
        activity_id: service.catalogActivityId,
        pricing_id: service.catalogPricingId
      })
      .select()
      .single();
    expect(linkCatalogPricingToActivity.data?.activity_id).toBe(
      service.catalogActivityId
    );
    expect(linkCatalogPricingToActivity.data?.pricing_id).toBe(
      service.catalogPricingId
    );

    const selectActivityForProvider = await serviceClient
      .schema("app_provider")
      .from("activity")
      .insert({
        id: service.selectedActivityId,
        user_id: provider.data.id,
        activity_id: service.catalogActivityId
      })
      .select()
      .single();
    expect(selectActivityForProvider.data?.id).toBe(service.selectedActivityId);

    const createServiceModifierForProvider = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .insert({
        user_id: provider.data.id,
        id: service.providerServiceModifierId,
        activity_id: service.selectedActivityId,
        name: { en: "Test Modifier" },
        description: { en: "Test modifier description" },
        soda_amount: 10,
        status: "published"
      })
      .select()
      .single();
    expect(createServiceModifierForProvider.data?.id).toBe(
      service.providerServiceModifierId
    );

    const createServiceForProvider = await serviceClient
      .schema("app_provider")
      .from("service")
      .insert({
        id: service.providerServiceId,
        user_id: provider.data.id,
        activity_id: service.selectedActivityId,
        selected_service_id: service.catalogServiceId,
        name: { en: "Test Service" },
        description: { en: "Test service description" },
        soda_amount: 50,
        max_unit_count: 1,
        pricing_id: service.catalogPricingId,
        status: "published"
      })
      .select()
      .single();
    expect(createServiceForProvider.data?.id).toBe(service.providerServiceId);

    const approveServiceForProvider = await serviceClient
      .schema("app_provider")
      .from("approved_service")
      .insert({
        service_id: service.providerServiceId,
        user_id: provider.data.id
      })
      .select()
      .single();
    expect(approveServiceForProvider.data?.service_id).toBe(
      service.providerServiceId
    );

    const approveServiceModifierForProvider = await serviceClient
      .schema("app_provider")
      .from("approved_service_modifier")
      .insert({
        service_modifier_id: service.providerServiceModifierId,
        user_id: provider.data.id
      })
      .select()
      .single();
    expect(approveServiceModifierForProvider.data?.service_modifier_id).toBe(
      service.providerServiceModifierId
    );
  });

  afterAll(async () => {
    await serviceClient
      .schema("app_provider")
      .from("service")
      .delete()
      .eq("id", service.providerServiceId);
    await serviceClient
      .schema("app_provider")
      .from("activity")
      .delete()
      .eq("id", service.selectedActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity_service")
      .delete()
      .eq("activity_id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity_pricing")
      .delete()
      .eq("activity_id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("activity")
      .delete()
      .eq("id", service.catalogActivityId);
    await serviceClient
      .schema("app_catalog")
      .from("service")
      .delete()
      .eq("id", service.catalogServiceId);
    await serviceClient
      .schema("app_catalog")
      .from("pricing")
      .delete()
      .eq("id", service.catalogPricingId);
  });

  return service;
}
