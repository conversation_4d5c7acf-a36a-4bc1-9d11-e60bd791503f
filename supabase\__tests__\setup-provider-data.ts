import { randomUUID } from "node:crypto";
import { afterAll, beforeAll, expect } from "vitest";
import { provider, serviceClient } from "./setup";

export const activityId = randomUUID();
export const serviceId = randomUUID();
export const pricingId = randomUUID();
export const selectedActivityId = randomUUID();
export const providerServiceModifierId = randomUUID();
export const providerServiceId = randomUUID();

beforeAll(async () => {
  // insert provider as approved
  const providerInsert = await serviceClient
    .schema("app_provider")
    .from("approved_user")
    .insert({ user_id: provider.id })
    .select()
    .single();

  expect(providerInsert.data?.user_id).toBe(provider.id);

  // insert provider status as open for orders
  const providerStatusInsert = await serviceClient
    .schema("app_provider")
    .from("status")
    .insert({
      user_id: provider.id,
      is_open_for_orders: true
    })
    .select()
    .single();

  expect(providerStatusInsert.data?.user_id).toBe(provider.id);

  // Create a catalog activity.
  const activityInsert = await serviceClient
    .schema("app_catalog")
    .from("activity")
    .insert({
      id: activityId,
      name: { en: "Test Activity" }
    })
    .select()
    .single();

  expect(activityInsert.data?.id).toBe(activityId);

  // Create a catalog service.
  const serviceInsert = await serviceClient
    .schema("app_catalog")
    .from("service")
    .insert({
      id: serviceId,
      name: { en: "Test Catalog Service" }
    })
    .select()
    .single();

  expect(serviceInsert.data?.id).toBe(serviceId);

  // Create a catalog pricing
  const pricingInsert = await serviceClient
    .schema("app_catalog")
    .from("pricing")
    .insert({
      id: pricingId,
      name: { en: "Test Pricing" }
    })
    .select()
    .single();

  expect(pricingInsert.data?.id).toBe(pricingId);

  // Link activity and service
  const activityServiceInsert = await serviceClient
    .schema("app_catalog")
    .from("activity_service")
    .insert({
      activity_id: activityId,
      service_id: serviceId
    })
    .select()
    .single();

  expect(activityServiceInsert.data?.activity_id).toBe(activityId);
  expect(activityServiceInsert.data?.service_id).toBe(serviceId);

  // Link pricing and activity
  const activityPricingInsert = await serviceClient
    .schema("app_catalog")
    .from("activity_pricing")
    .insert({
      activity_id: activityId,
      pricing_id: pricingId
    })
    .select()
    .single();

  expect(activityPricingInsert.data?.activity_id).toBe(activityId);
  expect(activityPricingInsert.data?.pricing_id).toBe(pricingId);

  // Create a provider selected activity
  const selectedActivityInsert = await serviceClient
    .schema("app_provider")
    .from("activity")
    .insert({
      id: selectedActivityId,
      user_id: provider.id,
      activity_id: activityId
    })
    .select()
    .single();

  expect(selectedActivityInsert.data?.id).toBe(selectedActivityId);

  // Create a provider service modifier
  const serviceModifierInsert = await serviceClient
    .schema("app_provider")
    .from("service_modifier")
    .insert({
      user_id: provider.id,
      id: providerServiceModifierId,
      activity_id: selectedActivityId,
      name: { en: "Test Modifier" },
      description: { en: "Test modifier description" },
      soda_amount: 10,
      status: "published"
    })
    .select()
    .single();

  expect(serviceModifierInsert.data?.id).toBe(providerServiceModifierId);

  // Create a provider service
  const providerServiceInsert = await serviceClient
    .schema("app_provider")
    .from("service")
    .insert({
      id: providerServiceId,
      user_id: provider.id,
      activity_id: selectedActivityId,
      selected_service_id: serviceId,
      name: { en: "Test Service" },
      description: { en: "Test service description" },
      soda_amount: 50,
      max_unit_count: 1,
      pricing_id: pricingId,
      status: "published"
    })
    .select()
    .single();

  expect(providerServiceInsert.data?.id).toBe(providerServiceId);

  // Add provider service to approved
  const approvedServiceInsert = await serviceClient
    .schema("app_provider")
    .from("approved_service")
    .insert({
      service_id: providerServiceId,
      user_id: provider.id
    })
    .select()
    .single();

  expect(approvedServiceInsert.data?.service_id).toBe(providerServiceId);

  // Add provider service modifier to approved
  const approvedServiceModifierInsert = await serviceClient
    .schema("app_provider")
    .from("approved_service_modifier")
    .insert({
      service_modifier_id: providerServiceModifierId,
      user_id: provider.id
    })
    .select()
    .single();

  expect(approvedServiceModifierInsert.data?.service_modifier_id).toBe(
    providerServiceModifierId
  );
});

afterAll(async () => {
  // Clean up created rows
  await serviceClient
    .schema("app_provider")
    .from("service")
    .delete()
    .eq("id", providerServiceId);
  await serviceClient
    .schema("app_provider")
    .from("activity")
    .delete()
    .eq("id", selectedActivityId);
  await serviceClient
    .schema("app_catalog")
    .from("activity_service")
    .delete()
    .eq("activity_id", activityId);
  await serviceClient
    .schema("app_catalog")
    .from("activity_pricing")
    .delete()
    .eq("activity_id", activityId);
  await serviceClient
    .schema("app_catalog")
    .from("activity")
    .delete()
    .eq("id", activityId);
  await serviceClient
    .schema("app_catalog")
    .from("service")
    .delete()
    .eq("id", serviceId);
  await serviceClient
    .schema("app_catalog")
    .from("pricing")
    .delete()
    .eq("id", pricingId);
});
