import { test, expect, describe, afterEach, beforeEach } from "vitest";
import {
  provider,
  providerClient,
  serviceClient,
  user,
  userClient,
  adminClient,
  otherUserClient,
  otherUser,
  admin
} from "./setup";
import {
  providerServiceId,
  providerServiceModifierId
} from "./setup-provider-data";
import { Database } from "shared/lib/supabase/database";

const logsToClean: string[] = [];

beforeEach(async function resetBalances() {
  // reset balances
  await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .upsert([
      { user_id: user.id, soda_balance: 1000 },
      { user_id: provider.id, soda_balance: 0 }
    ]);
});

afterEach(async function cleanOrders() {
  // clean orders
  await serviceClient
    .schema("app_provider")
    .from("order")
    .delete()
    .eq("receiver_id", provider.id);

  // clean order archive
  await serviceClient
    .schema("app_provider")
    .from("order_archive")
    .delete()
    .eq("receiver_id", provider.id);

  // clean order logs
  await serviceClient
    .schema("app_provider")
    .from("order_log")
    .delete()
    .in("order_id", logsToClean);
});

describe("submitting order", () => {
  test("sufficient balance can submit", async () => {
    const order = await userClient.schema("app_provider").rpc("submit_order", {
      p_service_id: providerServiceId,
      p_unit_count: 1
    });

    expect(order.data?.id).toBeDefined();
    logsToClean.push(String(order.data?.id));
  });

  test("insufficient balance cannot submit", async () => {
    // Set user's balance to a low amount
    await serviceClient.schema("app_transaction").from("wallet").upsert({
      user_id: user.id,
      soda_balance: 10 // Less than the service cost (50)
    });

    // Attempt to submit the order and expect an error
    const order = await userClient.schema("app_provider").rpc("submit_order", {
      p_service_id: providerServiceId,
      p_unit_count: 1
    });

    expect(order.error).toBeDefined();
  });
});

describe("order progression", () => {
  describe("for sender", () => {
    test("pending -> cancelled => escrow refund", async () => {
      const orderInsert = await userClient
        .schema("app_provider")
        .rpc("submit_order", {
          p_service_id: providerServiceId,
          p_unit_count: 1
        });

      expect(orderInsert.data?.order_status).toBe("pending");

      const orderId = String(orderInsert.data?.id);
      logsToClean.push(orderId);

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(950);

      const cancelUpdate = await userClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "cancelled" })
        .eq("id", orderId);

      expect(cancelUpdate.error).toBeNull();

      // Check sender's balance - should be refunded
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000); // Balance should be back to initial value.
    });

    test("accepted -> in_dispute", async () => {
      // Submit an order as the user
      const orderInsert = await userClient
        .schema("app_provider")
        .rpc("submit_order", {
          p_service_id: providerServiceId,
          p_unit_count: 1
        });

      expect(orderInsert.data?.order_status).toBe("pending");
      const orderId = String(orderInsert.data?.id);
      logsToClean.push(orderId);
      const escrowId = orderInsert.data?.escrow_id;

      // Accept the order as the provider
      const acceptUpdate = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", orderId);

      expect(acceptUpdate.error).toBeNull();

      // Initiate a dispute as the user
      const disputeUpdate = await userClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "in_dispute" })
        .eq("id", orderId);

      expect(disputeUpdate.error).toBeNull();

      // Assert the order status is 'in_dispute'
      const orderSelect = await serviceClient
        .schema("app_provider")
        .from("order")
        .select("order_status")
        .eq("id", orderId)
        .single();

      expect(orderSelect.data?.order_status).toBe("in_dispute");

      // Assert the escrow is still pending
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data?.status).toBe("pending");
    });

    test("completed -> disputable period ends ->| in_dispute", async () => {
      const orderInsert = await serviceClient
        .schema("app_provider")
        .from("order")
        .insert({
          sender_id: user.id,
          receiver_id: provider.id,
          service_id: providerServiceId,
          order_status: "completed",
          soda_amount: 50,
          unit_count: 1,
          completed_at: "2023-10-26T10:00:00Z"
        })
        .select()
        .single();

      expect(orderInsert.error).toBeNull();
      const orderId = String(orderInsert.data?.id);
      logsToClean.push(orderId);

      // Attempt to update the order status to 'in_dispute' as the user
      const updateResult = await userClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "in_dispute" })
        .eq("id", orderId)
        .select()
        .single();

      // Assert that the update failed with an error
      expect(updateResult.error).not.toBeNull();
    });
  });

  describe("for receiver", () => {
    test("pending -> rejected => escrow refund", async () => {
      const orderInsert = await userClient
        .schema("app_provider")
        .rpc("submit_order", {
          p_service_id: providerServiceId,
          p_unit_count: 1
        });

      expect(orderInsert.data?.order_status).toBe("pending");

      const orderId = String(orderInsert.data?.id);
      logsToClean.push(orderId);

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(950);

      const rejectUpdate = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "rejected" })
        .eq("id", orderId);

      expect(rejectUpdate.error).toBeNull();

      // Check sender's balance - should be refunded
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000);
    });

    test("completed -> disputable period ends => escrow release", async () => {
      // create an order with a completed status and a disputable_until in the past.
      const orderInsert = await serviceClient
        .schema("app_provider")
        .from("order")
        .insert({
          sender_id: user.id,
          receiver_id: provider.id,
          service_id: providerServiceId,
          order_status: "completed",
          soda_amount: 50,
          unit_count: 1,
          created_at: "2023-10-26T10:00:00Z",
          completed_at: "2023-10-26T10:00:00Z"
        })
        .select()
        .single();
      logsToClean.push(String(orderInsert.data?.id));

      const escrowId = orderInsert.data?.escrow_id;

      // Check escrow table for pending escrow.
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data?.status).toBe("pending");

      // run the function
      const releaseResult = await serviceClient
        .schema("app_provider")
        .rpc("release_completed_order_escrow");

      expect(releaseResult.error).toBeNull();

      // check that the order's escrow is released.
      const escrowSelectAgain = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelectAgain.data).toBeNull();

      // Check balances.
      const senderWallet = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWallet.data?.soda_balance).toBe(950);

      const receiverWallet = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", provider.id)
        .single();

      expect(receiverWallet.data?.soda_balance).toBe(50);
    });

    test("in_dispute -> refunded: refund escrow", async () => {
      // Submit an order as the user
      const orderInsert = await userClient
        .schema("app_provider")
        .rpc("submit_order", {
          p_service_id: providerServiceId,
          p_unit_count: 1
        });

      expect(orderInsert.data?.order_status).toBe("pending");

      const orderId = String(orderInsert.data?.id);
      logsToClean.push(orderId);
      const escrowId = orderInsert.data?.escrow_id;

      // Check sender's balance - should be reduced
      const senderWalletBefore = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletBefore.data?.soda_balance).toBe(950);

      // Accept the order as the provider
      const acceptUpdate = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", orderId)
        .select()
        .single();

      expect(acceptUpdate.data?.order_status).toBe("accepted");

      // Complete the order as the provider
      const completeUpdate = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "completed" })
        .eq("id", orderId)
        .select()
        .single();

      expect(completeUpdate.data?.order_status).toBe("completed");

      // Initiate a dispute as the user
      const disputeUpdate = await userClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "in_dispute" })
        .eq("id", orderId)
        .select()
        .single();

      expect(disputeUpdate.data?.order_status).toBe("in_dispute");

      // Refund the order as the provider
      const refundUpdate = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "refunded" })
        .eq("id", orderId)
        .select()
        .single();

      expect(refundUpdate.data?.order_status).toBe("refunded");

      // Assert the escrow is released (deleted)
      const escrowSelect = await serviceClient
        .schema("app_transaction")
        .from("escrow")
        .select("*")
        .eq("id", String(escrowId))
        .single();

      expect(escrowSelect.data).toBeNull();

      // Assert the sender's balance is back to the initial amount
      const senderWalletAfter = await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single();

      expect(senderWalletAfter.data?.soda_balance).toBe(1000);
    });
  });
});

describe("order prohibited transitions", () => {
  const senderProhibitedTransitions: { from: string; to: string[] }[] = [
    {
      from: "pending",
      to: ["accepted", "rejected", "completed", "in_dispute", "refunded"]
    },
    {
      from: "accepted",
      to: ["pending", "rejected", "completed", "cancelled", "refunded"]
    },
    {
      from: "rejected",
      to: [
        "pending",
        "accepted",
        "completed",
        "cancelled",
        "in_dispute",
        "refunded"
      ]
    },
    {
      from: "completed",
      to: ["pending", "accepted", "rejected", "cancelled", "refunded"]
    },
    {
      from: "cancelled",
      to: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "in_dispute",
        "refunded"
      ]
    },
    {
      from: "in_dispute",
      to: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "cancelled",
        "refunded"
      ]
    },
    {
      from: "refunded",
      to: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "cancelled",
        "in_dispute"
      ]
    }
  ];

  const receiverProhibitedTransitions: { from: string; to: string[] }[] = [
    {
      from: "pending",
      to: ["completed", "cancelled", "in_dispute", "refunded"]
    },
    {
      from: "accepted",
      to: ["pending", "rejected", "cancelled", "in_dispute", "refunded"]
    },
    {
      from: "rejected",
      to: [
        "pending",
        "accepted",
        "completed",
        "cancelled",
        "in_dispute",
        "refunded"
      ]
    },
    {
      from: "completed",
      to: ["pending", "accepted", "rejected", "cancelled", "in_dispute"]
    },
    {
      from: "cancelled",
      to: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "in_dispute",
        "refunded"
      ]
    },
    {
      from: "in_dispute",
      to: ["pending", "accepted", "rejected", "completed", "cancelled"]
    },
    {
      from: "refunded",
      to: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "cancelled",
        "in_dispute"
      ]
    }
  ];

  type OrderStatus = Database["app_provider"]["Enums"]["order_status"];

  describe("for sender", () => {
    senderProhibitedTransitions.forEach(({ from, to }) => {
      to.forEach((targetStatus) => {
        test(`${from} ->| ${targetStatus}`, async () => {
          // Insert an order with the 'from' status
          const orderInsert = await serviceClient
            .schema("app_provider")
            .from("order")
            .insert({
              sender_id: user.id,
              receiver_id: provider.id,
              service_id: providerServiceId,
              order_status: from as OrderStatus, // Use the 'from' status from the loop
              soda_amount: 50,
              unit_count: 1
            })
            .select()
            .single();
          logsToClean.push(String(orderInsert.data?.id));

          expect(orderInsert.error).toBeNull();
          const orderId = String(orderInsert.data?.id);

          // Attempt to update the order status to the 'targetStatus' as the sender
          const updateResult = await userClient
            .schema("app_provider")
            .from("order")
            .update({ order_status: targetStatus as OrderStatus }) // Use the 'targetStatus' from the loop
            .eq("id", orderId);

          // Assert that the update failed with an error
          expect(updateResult.error).not.toBeNull();
        });
      });
    });
  });

  describe("for receiver", () => {
    receiverProhibitedTransitions.forEach(({ from, to }) => {
      to.forEach((targetStatus) => {
        test(`${from} ->| ${targetStatus}`, async () => {
          // Insert an order with the 'from' status
          const orderInsert = await serviceClient
            .schema("app_provider")
            .from("order")
            .insert({
              sender_id: user.id,
              receiver_id: provider.id,
              service_id: providerServiceId,
              order_status: from as OrderStatus, // Use the 'from' status from the loop
              soda_amount: 50,
              unit_count: 1
            })
            .select()
            .single();
          logsToClean.push(String(orderInsert.data?.id));

          expect(orderInsert.error).toBeNull();
          const orderId = String(orderInsert.data?.id);

          // Attempt to update the order status to the 'targetStatus' as the receiver
          const updateResult = await providerClient
            .schema("app_provider")
            .from("order")
            .update({ order_status: targetStatus as OrderStatus }) // Use the 'targetStatus' from the loop
            .eq("id", orderId);

          // Assert that the update failed with an error
          expect(updateResult.error).not.toBeNull();
        });
      });
    });
  });
});

test("archive order on delete", async () => {
  // Insert an order using submit_order.
  const newOrder = await userClient.schema("app_provider").rpc("submit_order", {
    p_service_id: providerServiceId,
    p_unit_count: 1
  });

  expect(newOrder.data?.order_status).toBe("pending");
  logsToClean.push(String(newOrder.data?.id));

  // Set order to cancelled
  const orderId = String(newOrder.data?.id);

  const cancelUpdate = await userClient
    .schema("app_provider")
    .from("order")
    .update({ order_status: "cancelled" })
    .eq("id", orderId);

  expect(cancelUpdate.error).toBeNull();

  // Check the order is in the archive.
  const archivedOrderSelect = await serviceClient
    .schema("app_provider")
    .from("order_archive")
    .select("*")
    .eq("id", orderId)
    .single();

  expect(archivedOrderSelect.data?.order_status).toBe("cancelled");
});

test("cost details are calculated correctly", async () => {
  // Insert an order using submit_order.
  const newOrder = await userClient.schema("app_provider").rpc("submit_order", {
    p_service_id: providerServiceId,
    p_unit_count: 2,
    p_service_modifier_ids: [providerServiceModifierId]
  });

  expect(newOrder.data?.order_status).toBe("pending");
  logsToClean.push(String(newOrder.data?.id));

  // Check the order details
  expect(newOrder.data?.order_details).toEqual(
    expect.objectContaining({
      service: expect.objectContaining({
        id: providerServiceId,
        name: { en: "Test Service" },
        soda_amount: 50,
        pricing: { en: "Test Pricing" }
      }),
      modifiers: expect.arrayContaining([
        expect.objectContaining({
          id: providerServiceModifierId,
          name: { en: "Test Modifier" },
          soda_amount: 10
        })
      ]), // Check if the modifier is included in the order details
      unit_count: 2
    })
  );
});

test("insert_order should not be callable", async () => {
  const errorMessage = "insert_order() can only be called from submit_order()";

  // user cant call
  const insertOrder = await userClient
    .schema("app_provider")
    .rpc("insert_order", {
      p_receiver_id: provider.id,
      p_sender_id: user.id,
      p_service_id: providerServiceId,
      p_soda_amount: 50,
      p_unit_count: 1,
      p_order_details: {}
    });

  expect(insertOrder.error?.message).toBe(errorMessage);

  // provider cant call
  const insertOrder2 = await providerClient
    .schema("app_provider")
    .rpc("insert_order", {
      p_receiver_id: provider.id,
      p_sender_id: user.id,
      p_service_id: providerServiceId,
      p_soda_amount: 50,
      p_unit_count: 1,
      p_order_details: {}
    });

  expect(insertOrder2.error?.message).toBe(errorMessage);

  // admin cant call
  const insertOrder3 = await serviceClient
    .schema("app_provider")
    .rpc("insert_order", {
      p_receiver_id: provider.id,
      p_sender_id: user.id,
      p_service_id: providerServiceId,
      p_soda_amount: 50,
      p_unit_count: 1,
      p_order_details: {}
    });

  expect(insertOrder3.error?.message).toBe(errorMessage);

  // service cant call
  const insertOrder4 = await serviceClient
    .schema("app_provider")
    .rpc("insert_order", {
      p_receiver_id: provider.id,
      p_sender_id: user.id,
      p_service_id: providerServiceId,
      p_soda_amount: 50,
      p_unit_count: 1,
      p_order_details: {}
    });

  expect(insertOrder4.error?.message).toBe(errorMessage);
});

test("cannot order if provider is not open for orders", async () => {
  // Set provider to not open for orders
  const providerStatusUpdate = await serviceClient
    .schema("app_provider")
    .from("status")
    .update({ is_open_for_orders: false })
    .eq("user_id", provider.id);

  expect(providerStatusUpdate.error).toBeNull();

  // Try to order
  const order = await userClient.schema("app_provider").rpc("submit_order", {
    p_service_id: providerServiceId,
    p_unit_count: 1
  });

  expect(order.error).not.toBeNull();

  // reset
  const providerStatusUpdate2 = await serviceClient
    .schema("app_provider")
    .from("status")
    .update({ is_open_for_orders: true })
    .eq("user_id", provider.id);

  expect(providerStatusUpdate2.error).toBeNull();
});

describe("admin", () => {
  let disputedOrderId: string;
  let disputedOrderEscrowId: string | null;

  beforeEach(async () => {
    // Ensure user and provider have enough soda
    await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .upsert([
        { user_id: user.id, soda_balance: 1000 },
        { user_id: provider.id, soda_balance: 0 }
      ]);

    // 1. User submits an order
    const orderInsert = await userClient
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: providerServiceId,
        p_unit_count: 1
      });
    expect(orderInsert.data?.order_status).toBe("pending");
    disputedOrderId = String(orderInsert.data?.id);
    disputedOrderEscrowId = orderInsert.data?.escrow_id || null;
    logsToClean.push(disputedOrderId);

    // 2. Provider accepts the order
    const acceptUpdate = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "accepted" })
      .eq("id", disputedOrderId);
    expect(acceptUpdate.error).toBeNull();

    // 3. Provider completes the order
    const completeUpdate = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "completed" })
      .eq("id", disputedOrderId);
    expect(completeUpdate.error).toBeNull();

    // 4. User initiates a dispute
    const disputeUpdate = await userClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "in_dispute" })
      .eq("id", disputedOrderId);
    expect(disputeUpdate.error).toBeNull();

    // Verify the order is in dispute
    const orderCheck = await serviceClient
      .schema("app_provider")
      .from("order")
      .select("*")
      .eq("id", disputedOrderId)
      .single();
    expect(orderCheck.data?.order_status).toBe("in_dispute");
  });

  test("can view all orders", async () => {
    const { data: orders, error } = await adminClient
      .schema("app_provider")
      .from("order")
      .select("*");

    expect(error).toBeNull();
    expect(orders).toBeDefined();
    expect(orders?.length).toBeGreaterThan(0);

    const disputedOrder = orders?.find(
      (o: Database["app_provider"]["Tables"]["order"]["Row"]) =>
        o.id === disputedOrderId
    );
    expect(disputedOrder).toBeDefined();
    expect(disputedOrder?.order_status).toBe("in_dispute");
  });

  test("can refund disputed order", async () => {
    const initialSenderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single()
    ).data?.soda_balance;

    const { data: refundedOrder, error } = await adminClient
      .schema("app_provider")
      .rpc("handle_disputed_order", {
        p_order_id: disputedOrderId,
        p_action: "refund"
      });

    expect(error).toBeNull();
    expect(refundedOrder?.order_status).toBe("refunded");

    // Verify sender's balance is refunded
    const finalSenderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", user.id)
        .single()
    ).data?.soda_balance;

    expect(finalSenderBalance).toBe(
      initialSenderBalance! + refundedOrder!.soda_amount
    );

    // Verify escrow is deleted
    const escrowCheck = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(disputedOrderEscrowId));
    expect(escrowCheck.data).toHaveLength(0);
  });

  test("can release disputed order (payment)", async () => {
    const initialProviderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", provider.id)
        .single()
    ).data?.soda_balance;

    const { data: releasedOrder, error } = await adminClient
      .schema("app_provider")
      .rpc("handle_disputed_order", {
        p_order_id: disputedOrderId,
        p_action: "release"
      });

    expect(error).toBeNull();
    expect(releasedOrder?.order_status).toBe("completed");

    // Verify provider's balance is credited
    const finalProviderBalance = (
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .select("soda_balance")
        .eq("user_id", provider.id)
        .single()
    ).data?.soda_balance;

    expect(finalProviderBalance).toBe(
      initialProviderBalance! + releasedOrder!.soda_amount
    );

    // Verify escrow is deleted
    const escrowCheck = await serviceClient
      .schema("app_transaction")
      .from("escrow")
      .select("*")
      .eq("id", String(disputedOrderEscrowId));
    expect(escrowCheck.data).toHaveLength(0);
  });
});

describe("user", () => {
  beforeEach(async () => {
    const orderInsert = await userClient
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: providerServiceId,
        p_unit_count: 1
      });
    expect(orderInsert.data?.order_status).toBe("pending");
    logsToClean.push(String(orderInsert.data?.id));

    // create an order for other user
    await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .upsert([
        { user_id: otherUser.id, soda_balance: 1000 },
        { user_id: provider.id, soda_balance: 0 }
      ]);

    const otherOrderInsert = await otherUserClient
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: providerServiceId,
        p_unit_count: 1
      });
    expect(otherOrderInsert.data?.order_status).toBe("pending");
    logsToClean.push(String(otherOrderInsert.data?.id));
  });

  test("cannot view others' orders", async () => {
    const { data: orders } = await userClient
      .schema("app_provider")
      .from("order")
      .select("*");

    // can only see their own order
    expect(orders).toHaveLength(1);
  });
});

describe("log order status change", () => {
  let order: Database["app_provider"]["Functions"]["submit_order"]["Returns"];

  beforeEach(async () => {
    const { data } = await userClient
      .schema("app_provider")
      .rpc("submit_order", {
        p_service_id: providerServiceId,
        p_unit_count: 1
      });

    if (!data) throw new Error("Order not created");

    order = data;

    logsToClean.push(order.id);
  });

  test("cancelled order", async () => {
    const { error } = await userClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "cancelled" })
      .eq("id", order.id);

    expect(error).toBeNull();

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", order.id)
      .eq("new_status", "cancelled")
      .single();

    expect(log?.new_status).toBe("cancelled");
    expect(log?.changed_by).toBe(user.id);
  });

  test("rejected order", async () => {
    const { error } = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "rejected" })
      .eq("id", order.id);

    expect(error).toBeNull();

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", order.id)
      .eq("new_status", "rejected")
      .single();

    expect(log?.new_status).toBe("rejected");
    expect(log?.changed_by).toBe(provider.id);
  });

  test("accepted order", async () => {
    const { error } = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "accepted" })
      .eq("id", order.id);

    expect(error).toBeNull();

    const { data: log } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", order.id)
      .eq("new_status", "accepted")
      .single();

    expect(log?.new_status).toBe("accepted");
    expect(log?.changed_by).toBe(provider.id);
  });

  test("completed order", async () => {
    const { error } = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "accepted" })
      .eq("id", order.id);

    expect(error).toBeNull();

    const { error: completeError } = await providerClient
      .schema("app_provider")
      .from("order")
      .update({ order_status: "completed" })
      .eq("id", order.id);

    expect(completeError).toBeNull();

    const { data: logs } = await serviceClient
      .schema("app_provider")
      .from("order_log")
      .select("*")
      .eq("order_id", order.id)
      .eq("new_status", "completed")
      .single();

    expect(logs?.new_status).toBe("completed");
    expect(logs?.changed_by).toBe(provider.id);
  });

  describe("disputed order", () => {
    beforeEach(async () => {
      const { error } = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "accepted" })
        .eq("id", order.id);

      expect(error).toBeNull();

      const { error: completeError } = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "completed" })
        .eq("id", order.id);

      expect(completeError).toBeNull();

      // dispute started by user
      const { error: disputeError } = await userClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "in_dispute" })
        .eq("id", order.id);

      expect(disputeError).toBeNull();
    });

    test("refunded order by provider", async () => {
      const { error: refundError } = await providerClient
        .schema("app_provider")
        .from("order")
        .update({ order_status: "refunded" })
        .eq("id", order.id);

      expect(refundError).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", order.id)
        .eq("new_status", "refunded")
        .single();

      expect(log?.new_status).toBe("refunded");
      expect(log?.changed_by).toBe(provider.id);
    });

    test("released order by admin", async () => {
      const { error: releaseError } = await adminClient
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: order.id,
          p_action: "release"
        });

      expect(releaseError).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", order.id)
        .eq("old_status", "in_dispute")
        .eq("new_status", "completed")
        .single();

      expect(log?.new_status).toBe("completed");
      expect(log?.changed_by).toBe(admin.id);
    });

    test("refunded order by admin", async () => {
      const { error: releaseError } = await adminClient
        .schema("app_provider")
        .rpc("handle_disputed_order", {
          p_order_id: order.id,
          p_action: "refund"
        });

      expect(releaseError).toBeNull();

      const { data: log } = await serviceClient
        .schema("app_provider")
        .from("order_log")
        .select("*")
        .eq("order_id", order.id)
        .eq("old_status", "in_dispute")
        .eq("new_status", "refunded")
        .single();

      expect(log?.new_status).toBe("refunded");
      expect(log?.changed_by).toBe(admin.id);
    });
  });
});
