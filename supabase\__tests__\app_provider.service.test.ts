import { test, expect, beforeAll } from "vitest";
import {
  provider,
  providerClient,
  serviceClient,
  user,
  userClient
} from "./setup";
import { pricingId, selectedActivityId } from "./setup-provider-data";
import { randomUUID } from "node:crypto";
import { describe } from "node:test";

const providerServiceId = randomUUID();

beforeAll(async () => {
  // Create a provider service
  await serviceClient
    .schema("app_provider")
    .from("service")
    .insert({
      id: providerServiceId,
      user_id: provider.id,
      activity_id: selectedActivityId,
      selected_service_id: null,
      name: { en: "Test Service" },
      description: { en: "Test service description" },
      soda_amount: 50,
      max_unit_count: 1,
      pricing_id: pricingId,
      status: "published"
    });
});

test("cannot select if not approved", async () => {
  const { data } = await userClient
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", providerServiceId)
    .single();

  expect(data).toBeNull();
});

test("can select approved and published", async () => {
  await serviceClient.schema("app_provider").from("approved_service").insert({
    service_id: providerServiceId,
    user_id: provider.id
  });

  const { data } = await userClient
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", providerServiceId)
    .single();

  expect(data?.id).toBe(providerServiceId);
});

test("cannot select modified without approval even if published", async () => {
  const serviceUpdate = await providerClient
    .schema("app_provider")
    .from("service")
    .update({ name: { en: "Modified Service" }, status: "published" })
    .eq("id", providerServiceId);

  expect(serviceUpdate.error).toBeNull();

  const { data } = await userClient
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", providerServiceId)
    .single();

  expect(data).toBeNull();
});

test("cannot select modified in draft even if approved", async () => {
  const serviceUpdate = await providerClient
    .schema("app_provider")
    .from("service")
    .update({ name: { en: "Modified Service" }, status: "draft" })
    .eq("id", providerServiceId);

  expect(serviceUpdate.error).toBeNull();

  const serviceApprove = await serviceClient
    .schema("app_provider")
    .from("approved_service")
    .upsert({
      service_id: providerServiceId,
      user_id: provider.id
    });

  expect(serviceApprove.error).toBeNull();

  const { data } = await userClient
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", providerServiceId)
    .single();

  expect(data).toBeNull();
});

test("can select modified after approved and published", async () => {
  const serviceUpdate = await providerClient
    .schema("app_provider")
    .from("service")
    .update({ name: { en: "Modified Service" }, status: "published" })
    .eq("id", providerServiceId);

  expect(serviceUpdate.error).toBeNull();

  const serviceApprove = await serviceClient
    .schema("app_provider")
    .from("approved_service")
    .upsert({
      service_id: providerServiceId,
      user_id: provider.id
    });

  expect(serviceApprove.error).toBeNull();

  const { data } = await userClient
    .schema("app_provider")
    .from("service")
    .select("*")
    .eq("id", providerServiceId)
    .single();

  expect(data?.id).toBe(providerServiceId);
});

test("cannot publish without name and selected service", async () => {
  const publishInsert = await providerClient
    .schema("app_provider")
    .from("service")
    .insert({
      user_id: provider.id,
      activity_id: selectedActivityId,
      name: {}, // <==
      selected_service_id: null, // <==
      description: { en: "Test service description" },
      soda_amount: 50,
      max_unit_count: 1,
      pricing_id: pricingId,
      status: "published"
    });

  expect(publishInsert.error).not.toBeNull();

  const publishUpdate = await providerClient
    .schema("app_provider")
    .from("service")
    .update({
      name: {}, // <==
      selected_service_id: null, // <==
      pricing_id: pricingId,
      status: "published"
    })
    .eq("id", providerServiceId);

  expect(publishUpdate.error).not.toBeNull();
});

test("cannot publish without pricing", async () => {
  const publishInsert = await providerClient
    .schema("app_provider")
    .from("service")
    .insert({
      user_id: provider.id,
      activity_id: selectedActivityId,
      name: { en: "Test Service" },
      description: { en: "Test service description" },
      soda_amount: 50,
      max_unit_count: 1,
      pricing_id: null, // <==
      status: "published"
    });

  expect(publishInsert.error).not.toBeNull();

  const publishUpdate = await providerClient
    .schema("app_provider")
    .from("service")
    .update({
      name: { en: "Test Service" },
      pricing_id: null, // <==
      status: "published"
    })
    .eq("id", providerServiceId);

  expect(publishUpdate.error).not.toBeNull();
});

describe("submit_order", () => {
  const draftServiceId = randomUUID();
  const draftServiceModifierId = randomUUID();

  beforeAll(async () => {
    // insert a draft service
    await serviceClient
      .schema("app_provider")
      .from("service")
      .insert({
        id: draftServiceId,
        user_id: provider.id,
        activity_id: selectedActivityId,
        selected_service_id: null,
        name: { en: "Draft Service" },
        description: { en: "Draft service description" },
        soda_amount: 50,
        max_unit_count: 1,
        pricing_id: pricingId,
        status: "draft"
      });

    // insert a draft service modifier
    await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .insert({
        id: draftServiceModifierId,
        user_id: provider.id,
        activity_id: selectedActivityId,
        name: { en: "Draft Modifier" },
        description: { en: "Draft modifier description" },
        soda_amount: 10,
        status: "draft"
      });

    // give enough soda to user
    await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .upsert({ user_id: user.id, soda_balance: 1000 });
  });

  test("Cannot order if the service is not published and approved", async () => {
    const order = await userClient.schema("app_provider").rpc("submit_order", {
      p_service_id: draftServiceId,
      p_unit_count: 1
    });

    expect(order.error).not.toBeNull();
  });

  test("Cannot order if the service modifier is not published and approved", async () => {
    const order = await userClient.schema("app_provider").rpc("submit_order", {
      p_service_id: providerServiceId,
      p_unit_count: 1,
      p_service_modifier_ids: [draftServiceModifierId]
    });

    expect(order.error).not.toBeNull();
  });
});
