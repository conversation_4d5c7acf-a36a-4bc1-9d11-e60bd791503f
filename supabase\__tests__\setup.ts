import { createClient as createService<PERSON>lient } from "shared/lib/supabase/service";
import { createClient as createBrowserClient } from "@supabase/supabase-js";
import { load } from "dotenv-mono";
import { beforeAll, afterAll, afterEach } from "vitest";
import { User, SupabaseClient } from "@supabase/supabase-js";
import { Database } from "shared/lib/supabase/database";
import { Client } from "pg";
import { beforeEach } from "node:test";

load();

// Service client for bypassing RLS
export let serviceClient: SupabaseClient<Database>;

// Browser clients for user actions
export let userClient: SupabaseClient<Database>;
export let user: User;
export let providerClient: SupabaseClient<Database>;
export let provider: User;
export let adminClient: SupabaseClient<Database>;
export let admin: User;
export let otherUserClient: SupabaseClient<Database>;
export let otherUser: User;

// Database connection configuration (from postgrestools.jsonc)
const dbConfig = {
  host: "127.0.0.1",
  port: 54322,
  user: "postgres",
  password: "postgres",
  database: "postgres"
};

export const dbClient = new Client(dbConfig);

// Helper function to create a user and sign in with a browser client
async function createAndSignInUser(
  role: string
): Promise<{ client: SupabaseClient<Database>; user: User }> {
  const maxRetries = 5;
  const retryDelayMs = 1000; // 1 second

  let userCreation;
  for (let i = 0; i < maxRetries; i++) {
    try {
      userCreation = await serviceClient.auth.admin.createUser({
        email: `${role}-${Date.now()}@example.com`,
        password: "password123",
        email_confirm: true
      });

      if (userCreation.data.user && userCreation.data.user.email) {
        break; // User created successfully, exit loop
      }
    } catch (error) {
      console.warn(`Attempt ${i + 1} to create ${role} user failed:`, error);
    }
    await new Promise((resolve) => setTimeout(resolve, retryDelayMs));
  }

  if (
    !userCreation ||
    !userCreation.data.user ||
    !userCreation.data.user.email
  ) {
    throw `Failed to create ${role} user after ${maxRetries} attempts`;
  }

  const client = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  await client.auth.signInWithPassword({
    email: userCreation.data.user.email,
    password: "password123"
  });

  return { client, user: userCreation.data.user };
}

beforeAll(async () => {
  await dbClient.connect();

  await resetRateLimits();

  serviceClient = createServiceClient();

  // Use the helper function for each user type
  ({ client: userClient, user } = await createAndSignInUser("user"));

  ({ client: providerClient, user: provider } =
    await createAndSignInUser("provider"));

  ({ client: adminClient, user: admin } = await createAndSignInUser("admin"));

  ({ client: otherUserClient, user: otherUser } =
    await createAndSignInUser("otherUser"));

  // Assign the admin user to the app_account.admin table
  const adminInsert = await serviceClient
    .schema("app_access")
    .rpc("assign_role_to_user", {
      v_user_id: admin.id,
      v_role_name: "admin"
    });

  if (adminInsert.error) {
    throw `Error assigning admin user: ${adminInsert.error.message}`;
  }

  // Assign the provider user to the app_provider.profile table
  const providerInsert = await serviceClient
    .schema("app_access")
    .rpc("assign_role_to_user", {
      v_user_id: provider.id,
      v_role_name: "provider"
    });

  if (providerInsert.error) {
    throw `Error assigning provider user to app_provider.profile: ${providerInsert.error.message}`;
  }
});

beforeEach(async () => {
  await resetRateLimits();
});

afterEach(async () => {
  await resetRateLimits();
});

afterAll(async () => {
  await resetRateLimits();

  await dbClient.end();

  if (user) {
    await serviceClient.auth.admin.deleteUser(user.id);
  }
  if (provider) {
    await serviceClient.auth.admin.deleteUser(provider.id);
  }
  if (admin) {
    await serviceClient.auth.admin.deleteUser(admin.id);
  }
  if (otherUser) {
    await serviceClient.auth.admin.deleteUser(otherUser.id);
  }
});

export async function resetRateLimits() {
  await dbClient.query("DELETE FROM app_core.rate_limit");
}
