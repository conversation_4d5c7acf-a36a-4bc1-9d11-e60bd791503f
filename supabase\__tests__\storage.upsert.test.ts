import { test, expect, afterAll } from "vitest";
import { userClient, user } from "./setup";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";

const pngPath = "supabase/__tests__/objects/test-upsert.png";
const webpPath = "supabase/__tests__/objects/test-upsert.webp";

afterAll(async () => {
  await userClient.storage.from("avatar").remove([user.id]);
});

test("Object ids should be same after upsert.", async () => {
  const png = readFileSync(pngPath);
  const pngType = await fileTypeFromBuffer(png);
  const webp = readFileSync(webpPath);
  const webpType = await fileTypeFromBuffer(webp);

  const pngInsertion = await userClient.storage
    .from("avatar")
    .upload(user.id, png, {
      upsert: true,
      metadata: {
        description: "png"
      },
      contentType: pngType?.mime
    });

  expect(pngInsertion.error).toBeNull();

  const pngInfo = await userClient.storage.from("avatar").info(user.id);

  expect(pngInfo.data?.metadata?.description).toBe("png");

  const webpInsertion = await userClient.storage
    .from("avatar")
    .upload(user.id, webp, {
      upsert: true,
      metadata: {
        description: "webp"
      },
      contentType: webpType?.mime
    });

  expect(webpInsertion.error).toBeNull();

  const webpInfo = await userClient.storage.from("avatar").info(user.id);

  expect(webpInfo.data?.metadata?.description).toBe("webp");

  expect(pngInfo.data?.id).toBe(webpInfo.data?.id);
});
