import { afterEach, beforeEach, expect } from "vitest";
import { serviceClient } from "../setup";
import { MockOrder } from "./order";
import { MockUser } from "./user";

export type MockReview = {
  id: string;
};

export function mockApprovedReviewEach(
  order: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  customer: <PERSON><PERSON><PERSON><PERSON>,
  admin: <PERSON>ck<PERSON><PERSON>
) {
  const review: MockReview = { id: "" };

  beforeEach(async () => {
    if (!order.id) throw new Error("Order ID is null");
    if (!customer.client) throw new Error("User client is null");
    if (!customer.data) throw new Error("User data is null");
    if (!admin.client) throw new Error("Admin client is null");

    // Fetch the review_pass entry created by the order completion trigger
    const { data: reviewPassData, error: reviewPassError } = await serviceClient
      .schema("app_provider")
      .from("review_pass")
      .select("id")
      .eq("order_id", order.id)
      .single();

    expect(reviewPassError).toBeNull();

    if (!reviewPassData) throw new Error("Review pass data not found");

    const writeReview = await customer.client
      .schema("app_provider")
      .from("review")
      .insert({
        id: reviewPassData.id,
        user_id: customer.data.id,
        rating: 5,
        comment: "Great service!",
        comment_locale: "en"
      })
      .select()
      .single();

    expect(writeReview.data?.id).toBe(reviewPassData.id);

    const approveReview = await admin.client
      .schema("app_provider")
      .from("approved_review")
      .insert({
        review_id: reviewPassData.id,
        user_id: customer.data.id
      })
      .select()
      .single();

    expect(approveReview.data?.review_id).toBe(reviewPassData.id);

    review.id = reviewPassData.id;
  });

  afterEach(async () => {
    if (!order.id) throw new Error("Order ID is null");

    // Clean up review-related tables
    await serviceClient
      .schema("app_provider")
      .from("approved_review")
      .delete()
      .eq("review_id", order.id);

    await serviceClient
      .schema("app_provider")
      .from("review")
      .delete()
      .eq("id", order.id);

    await serviceClient
      .schema("app_provider")
      .from("review_pass")
      .delete()
      .eq("order_id", order.id);
  });

  return review;
}
