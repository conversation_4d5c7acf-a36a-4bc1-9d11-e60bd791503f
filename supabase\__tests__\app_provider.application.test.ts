import { test, expect, afterAll, describe, beforeAll } from "vitest";
import { userClient, user, serviceClient } from "./setup";
import { activityId, pricingId } from "./setup-provider-data";
import { randomUUID } from "node:crypto";

const providerActivityId = randomUUID();
const providerServiceId = randomUUID();
const providerServiceModifierId = randomUUID();
let providerApplicantRoleId: number;
let providerApplicantUnderReviewRoleId: number;

function createAndDeleteApplication() {
  beforeAll(async () => {
    // insert an application
    const insertApplication = await userClient
      .schema("app_provider")
      .from("application")
      .insert({
        user_id: user.id
      });

    expect(insertApplication.error).toBeNull();
  });

  afterAll(async () => {
    // clean application
    await serviceClient
      .schema("app_provider")
      .from("application")
      .delete()
      .eq("user_id", user.id);
  });
}

function createAndDeleteRequiredData() {
  beforeAll(async () => {
    const profileInsert = await userClient
      .schema("app_provider")
      .from("profile")
      .insert({
        user_id: user.id,
        slug: "test-profile",
        bio: { en: "Test bio" }
      })
      .select("*")
      .single();

    expect(profileInsert.data?.user_id).toBe(user.id);

    const activityInsert = await userClient
      .schema("app_provider")
      .from("activity")
      .insert({
        id: providerActivityId,
        user_id: user.id,
        activity_id: activityId
      })
      .select("*")
      .single();

    expect(activityInsert.data?.user_id).toBe(user.id);

    const serviceInsert = await userClient
      .schema("app_provider")
      .from("service")
      .insert({
        id: providerServiceId,
        user_id: user.id,
        activity_id: providerActivityId,
        name: { en: "Test Service" },
        description: { en: "Test service description" },
        soda_amount: 50,
        max_unit_count: 1,
        pricing_id: pricingId,
        status: "draft"
      })
      .select("*")
      .single();

    expect(serviceInsert.data?.user_id).toBe(user.id);

    const serviceModifierInsert = await userClient
      .schema("app_provider")
      .from("service_modifier")
      .insert({
        id: providerServiceModifierId,
        user_id: user.id,
        activity_id: providerActivityId,
        name: { en: "Test Modifier" },
        description: { en: "Test modifier description" },
        soda_amount: 10,
        status: "draft"
      })
      .select("*")
      .single();

    expect(serviceModifierInsert.data?.user_id).toBe(user.id);
  });

  afterAll(async () => {
    await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .delete()
      .eq("id", providerServiceModifierId);

    await serviceClient
      .schema("app_provider")
      .from("service")
      .delete()
      .eq("id", providerServiceId);

    await serviceClient
      .schema("app_provider")
      .from("activity")
      .delete()
      .eq("id", providerActivityId);

    await serviceClient
      .schema("app_provider")
      .from("profile")
      .delete()
      .eq("user_id", user.id);
  });
}

beforeAll(async function getRoleIds() {
  // get provider_applicant role id
  const providerApplicantRoleIdSelect = await serviceClient
    .schema("app_access")
    .from("role")
    .select("id")
    .eq("name", "provider_applicant")
    .single();

  providerApplicantRoleId = Number(providerApplicantRoleIdSelect.data?.id);

  expect(providerApplicantRoleId).toBeDefined();

  // get provider_applicant_under_review role id
  const providerApplicantUnderReviewRoleIdSelect = await serviceClient
    .schema("app_access")
    .from("role")
    .select("id")
    .eq("name", "provider_applicant_under_review")
    .single();

  providerApplicantUnderReviewRoleId = Number(
    providerApplicantUnderReviewRoleIdSelect.data?.id
  );

  expect(providerApplicantUnderReviewRoleId).toBeDefined();
});

describe("role", () => {
  describe("customer", () => {
    createAndDeleteApplication();

    test("user cannot insert application twice", async () => {
      // insert application
      const insertApplication = await userClient
        .schema("app_provider")
        .from("application")
        .insert({
          user_id: user.id,
          application_status: "pending"
        });

      expect(insertApplication.error).not.toBeNull();
    });

    test("user has two roles on application creation", async () => {
      // get customer roles
      const customerRoleIdSelect = await serviceClient
        .schema("app_access")
        .from("user_role")
        .select("*")
        .eq("user_id", user.id);

      // Should be 2 roles
      expect(customerRoleIdSelect.data?.length).toBe(2);
    });

    test("user acquires `provider_applicant` role on application creation", async () => {
      const userRoleSelect = await serviceClient
        .schema("app_access")
        .from("user_role")
        .select("*")
        .eq("user_id", user.id)
        .eq("role_id", providerApplicantRoleId)
        .single();

      expect(userRoleSelect.data?.role_id).toBe(providerApplicantRoleId);
    });

    test("user cannot submit application if no profile and service created", async () => {
      // submit application
      const applicationUpdate = await userClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();
    });
  });

  describe("provider_applicant", () => {
    createAndDeleteApplication();
    createAndDeleteRequiredData();

    test("can update profile, activity, create service and service modifier", async () => {
      // Check updating provider related data.
      const profileUpdate = await userClient
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(profileUpdate.data?.bio).toEqual({ en: "Updated bio" });

      const serviceUpdate = await userClient
        .schema("app_provider")
        .from("service")
        .update({ name: { en: "Updated Service" } })
        .eq("id", providerServiceId)
        .select("*")
        .single();

      expect(serviceUpdate.data?.name).toEqual({ en: "Updated Service" });

      const serviceModifierUpdate = await userClient
        .schema("app_provider")
        .from("service_modifier")
        .update({ name: { en: "Updated Modifier" } })
        .eq("id", providerServiceModifierId)
        .select("*")
        .single();

      expect(serviceModifierUpdate.data?.name).toEqual({
        en: "Updated Modifier"
      });
    });

    test("can submit application", async () => {
      // submit application
      const applicationUpdate = await userClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(applicationUpdate.data?.application_status).toBe("submitted");
    });
  });

  describe("provider_applicant_under_review", () => {
    createAndDeleteApplication();
    createAndDeleteRequiredData();

    beforeAll(async () => {
      // submit application
      const applicationUpdate = await userClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "submitted" })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(applicationUpdate.data?.application_status).toBe("submitted");
    });

    test("can view application, profile, activity, service and service modifier", async () => {
      const applicationSelect = await userClient
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", user.id)
        .single();

      expect(applicationSelect.data?.application_status).toBe("submitted");

      const profileSelect = await userClient
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", user.id)
        .single();

      expect(profileSelect.data?.user_id).toBe(user.id);

      const activitySelect = await userClient
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", providerActivityId)
        .single();

      expect(activitySelect.data?.id).toBe(providerActivityId);

      const serviceSelect = await userClient
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", providerServiceId)
        .single();

      expect(serviceSelect.data?.id).toBe(providerServiceId);

      const serviceModifierSelect = await userClient
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", providerServiceModifierId)
        .single();

      expect(serviceModifierSelect.data?.id).toBe(providerServiceModifierId);
    });

    test("cannot update or delete, application, profile, activity, service and service modifier", async () => {
      const applicationUpdate = await userClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "pending" })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).not.toBeNull();

      await userClient
        .schema("app_provider")
        .from("application")
        .delete()
        .eq("user_id", user.id);

      const applicationCheck = await userClient
        .schema("app_provider")
        .from("application")
        .select("*")
        .eq("user_id", user.id)
        .single();

      expect(applicationCheck.data?.user_id).toBe(user.id);

      // update profile
      const profileUpdate = await userClient
        .schema("app_provider")
        .from("profile")
        .update({ bio: { en: "Updated bio" } })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(profileUpdate.data?.bio).not.toEqual({ en: "Updated bio" });

      // delete profile
      await userClient
        .schema("app_provider")
        .from("profile")
        .delete()
        .eq("user_id", user.id);

      const profileCheck = await userClient
        .schema("app_provider")
        .from("profile")
        .select("*")
        .eq("user_id", user.id)
        .single();

      expect(profileCheck.data).toBeDefined();

      // delete activity
      await userClient
        .schema("app_provider")
        .from("activity")
        .delete()
        .eq("id", providerActivityId);

      const activitySelect = await userClient
        .schema("app_provider")
        .from("activity")
        .select("*")
        .eq("id", providerActivityId)
        .single();

      expect(activitySelect.data).toBeDefined();

      // update service
      const serviceUpdate = await userClient
        .schema("app_provider")
        .from("service")
        .update({ soda_amount: 66 })
        .eq("id", providerServiceId)
        .select()
        .single();

      expect(serviceUpdate.data?.soda_amount).not.toBe(66);

      // delete service
      await userClient
        .schema("app_provider")
        .from("service")
        .delete()
        .eq("id", providerServiceId);

      const serviceSelect = await userClient
        .schema("app_provider")
        .from("service")
        .select("*")
        .eq("id", providerServiceId)
        .single();

      expect(serviceSelect.data).toBeDefined();

      // update service modifier
      const serviceModifierUpdate = await userClient
        .schema("app_provider")
        .from("service_modifier")
        .update({ soda_amount: 66 })
        .eq("id", providerServiceModifierId)
        .select()
        .single();

      expect(serviceModifierUpdate.data?.soda_amount).not.toBe(66);

      // delete service modifier
      await userClient
        .schema("app_provider")
        .from("service_modifier")
        .delete()
        .eq("id", providerServiceModifierId);

      const serviceModifierSelect = await userClient
        .schema("app_provider")
        .from("service_modifier")
        .select("*")
        .eq("id", providerServiceModifierId)
        .single();

      expect(serviceModifierSelect.data).toBeDefined();
    });

    test("role switches to `provider` when application is approved", async () => {
      // approve application
      const applicationUpdate = await serviceClient
        .schema("app_provider")
        .from("application")
        .update({ application_status: "approved" })
        .eq("user_id", user.id)
        .select("*")
        .single();

      expect(applicationUpdate.error).toBeNull();
      expect(applicationUpdate.data?.application_status).toBe("approved");

      // get provider role id
      const providerRoleIdSelect = await serviceClient
        .schema("app_access")
        .from("role")
        .select("id")
        .eq("name", "provider")
        .single();

      const roleId = Number(providerRoleIdSelect.data?.id);

      expect(roleId).toBeDefined();

      const userRoleSelect = await serviceClient
        .schema("app_access")
        .from("user_role")
        .select("*")
        .eq("user_id", user.id)
        .eq("role_id", roleId)
        .single();

      expect(userRoleSelect.error).toBeNull();
      expect(userRoleSelect.data?.role_id).toBe(roleId);
    });
  });
});

describe("application rejection", () => {
  createAndDeleteApplication();
  createAndDeleteRequiredData();

  // submit application
  beforeAll(async () => {
    const applicationUpdate = await userClient
      .schema("app_provider")
      .from("application")
      .update({ application_status: "submitted" })
      .eq("user_id", user.id)
      .select("*")
      .single();

    expect(applicationUpdate.data?.application_status).toBe("submitted");
  });

  test("application roles are removed when application is rejected", async () => {
    // reject application
    const applicationUpdate = await serviceClient
      .schema("app_provider")
      .from("application")
      .update({ application_status: "rejected" })
      .eq("user_id", user.id)
      .select("*")
      .single();

    expect(applicationUpdate.error).toBeNull();
    expect(applicationUpdate.data?.application_status).toBe("rejected");

    // get provider_applicant role id
    const providerApplicantRoleIdSelect = await serviceClient
      .schema("app_access")
      .from("role")
      .select("id")
      .eq("name", "provider_applicant")
      .single();

    const providerApplicantRoleId = Number(
      providerApplicantRoleIdSelect.data?.id
    );

    expect(providerApplicantRoleId).toBeDefined();

    const providerApplicantUnderReviewRoleIdSelect = await serviceClient
      .schema("app_access")
      .from("role")
      .select("id")
      .eq("name", "provider_applicant_under_review")
      .single();

    const providerApplicantUnderReviewRoleId = Number(
      providerApplicantUnderReviewRoleIdSelect.data?.id
    );

    expect(providerApplicantUnderReviewRoleId).toBeDefined();

    const userRoleSelect = await serviceClient
      .schema("app_access")
      .from("user_role")
      .select("*")
      .eq("user_id", user.id)
      .or(
        `role_id.eq.${providerApplicantRoleId},role_id.eq.${providerApplicantUnderReviewRoleId}`
      );

    expect(userRoleSelect.data?.length).toBe(0);

    // Check if the customer role still exists
    const customerRoleIdSelect = await serviceClient
      .schema("app_access")
      .from("role")
      .select("id")
      .eq("name", "customer")
      .single();

    const customerRoleId = Number(customerRoleIdSelect.data?.id);

    expect(customerRoleId).toBeDefined();

    const customerRoleSelect = await serviceClient
      .schema("app_access")
      .from("user_role")
      .select("*")
      .eq("user_id", user.id)
      .eq("role_id", customerRoleId)
      .single();

    expect(customerRoleSelect.data).toBeDefined();
  });

  test("provider data is deleted when application is rejected", async () => {
    const applicationUpdate = await serviceClient
      .schema("app_provider")
      .from("application")
      .update({ application_status: "rejected" })
      .eq("user_id", user.id)
      .select("*")
      .single();

    expect(applicationUpdate.error).toBeNull();
    expect(applicationUpdate.data?.application_status).toBe("rejected");

    // Verify that related data is deleted
    const profileSelect = await serviceClient
      .schema("app_provider")
      .from("profile")
      .select("*")
      .eq("user_id", user.id);
    expect(profileSelect.data?.length).toBe(0);

    const activitySelect = await serviceClient
      .schema("app_provider")
      .from("activity")
      .select("*")
      .eq("user_id", user.id);
    expect(activitySelect.data?.length).toBe(0);

    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("user_id", user.id);
    expect(serviceSelect.data?.length).toBe(0);

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("user_id", user.id);
    expect(serviceModifierSelect.data?.length).toBe(0);
  });
});

describe("application approved", () => {
  createAndDeleteApplication();
  createAndDeleteRequiredData();

  // submit application
  beforeAll(async () => {
    const applicationUpdate = await serviceClient
      .schema("app_provider")
      .from("application")
      .update({ application_status: "approved" })
      .eq("user_id", user.id)
      .select("*")
      .single();

    expect(applicationUpdate.data?.application_status).toBe("approved");
  });

  test("service and service modifier are approved and their status are set to published", async () => {
    const serviceSelect = await serviceClient
      .schema("app_provider")
      .from("service")
      .select("*")
      .eq("id", providerServiceId)
      .single();

    expect(serviceSelect.data?.status).toBe("published");

    const serviceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("service_modifier")
      .select("*")
      .eq("id", providerServiceModifierId)
      .single();

    expect(serviceModifierSelect.data?.status).toBe("published");

    const approvedServiceSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service")
      .select("*")
      .eq("service_id", providerServiceId)
      .single();

    expect(approvedServiceSelect.data?.service_id).toBe(providerServiceId);

    const approvedServiceModifierSelect = await serviceClient
      .schema("app_provider")
      .from("approved_service_modifier")
      .select("*")
      .eq("service_modifier_id", providerServiceModifierId)
      .single();

    expect(approvedServiceModifierSelect.data?.service_modifier_id).toBe(
      providerServiceModifierId
    );
  });
});
